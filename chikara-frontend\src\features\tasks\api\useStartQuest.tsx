import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useStartQuest() {
    const queryClient = useQueryClient();

    return useMutation(
        api.quests.startQuest.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.quests.availableQuests.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.quests.getActive.key(),
                });
            },
            onError: (error) => {
                console.error("Failed to start quest:", error);
            },
        })
    );
}
