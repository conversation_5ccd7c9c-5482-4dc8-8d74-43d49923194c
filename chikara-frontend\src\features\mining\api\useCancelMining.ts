import { useMutation, useQueryClient } from "@tanstack/react-query";
import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";

const useCancelMining = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.mining.cancelMining.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: APIROUTES.MINING.SESSION });
            },
        })
    );
};

export default useCancelMining;
